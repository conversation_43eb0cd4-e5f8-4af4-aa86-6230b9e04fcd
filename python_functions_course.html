<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Python Functions - University CS Course</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/themes/prism-tomorrow.min.css">
    <script src="https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/components/prism-core.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/plugins/autoloader/prism-autoloader.min.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #333;
            background: #f5f5f5;
        }

        .slide {
            width: 100vw;
            height: 100vh;
            padding: 80px 60px;
            display: flex;
            flex-direction: column;
            justify-content: center;
            page-break-after: always;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            position: relative;
        }

        .slide.content {
            background: white;
            color: #333;
            justify-content: flex-start;
            padding-top: 100px;
        }

        .slide h1 {
            font-size: 4em;
            margin-bottom: 40px;
            text-align: center;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
            font-weight: 700;
        }

        .slide h2 {
            font-size: 3em;
            margin-bottom: 40px;
            color: #2c3e50;
            border-bottom: 4px solid #3498db;
            padding-bottom: 15px;
            font-weight: 600;
        }

        .slide h3 {
            font-size: 2.2em;
            margin-bottom: 30px;
            color: #34495e;
            font-weight: 500;
        }

        .slide p, .slide li {
            font-size: 1.6em;
            margin-bottom: 20px;
            line-height: 1.8;
        }

        .slide ul {
            margin-left: 50px;
            margin-bottom: 30px;
        }

        .slide li {
            margin-bottom: 15px;
            padding-left: 10px;
        }

        /* Code styling with syntax highlighting */
        pre[class*="language-"] {
            background: #2d3748 !important;
            border-radius: 12px;
            padding: 30px !important;
            margin: 30px 0 !important;
            font-size: 1.3em !important;
            line-height: 1.6 !important;
            border-left: 6px solid #3498db;
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
            overflow-x: auto;
        }

        code[class*="language-"] {
            color: #e2e8f0 !important;
            font-family: 'Fira Code', 'Consolas', 'Monaco', monospace !important;
        }

        /* Python syntax highlighting */
        .token.keyword {
            color: #ff7979 !important;
            font-weight: bold !important;
        }

        .token.string {
            color: #6c5ce7 !important;
        }

        .token.function {
            color: #00b894 !important;
        }

        .token.number {
            color: #fdcb6e !important;
        }

        .token.comment {
            color: #74b9ff !important;
            font-style: italic !important;
        }

        .token.operator {
            color: #fd79a8 !important;
        }

        .highlight {
            background: #f39c12;
            color: white;
            padding: 8px 15px;
            border-radius: 8px;
            font-weight: bold;
            display: inline-block;
            margin: 5px;
        }

        .slide-number {
            position: absolute;
            bottom: 30px;
            right: 40px;
            font-size: 1.4em;
            opacity: 0.8;
            background: rgba(0,0,0,0.1);
            padding: 10px 15px;
            border-radius: 20px;
        }

        .title-slide {
            text-align: center;
        }

        .title-slide h1 {
            font-size: 5em;
            margin-bottom: 30px;
        }

        .title-slide .subtitle {
            font-size: 2.2em;
            margin-bottom: 50px;
            opacity: 0.9;
        }

        .title-slide .info {
            font-size: 1.8em;
            opacity: 0.8;
        }

        .example-box {
            background: #ecf0f1;
            border: 3px solid #3498db;
            border-radius: 15px;
            padding: 30px;
            margin: 30px 0;
            font-size: 1.4em;
        }

        .warning-box {
            background: #ffe6e6;
            border: 3px solid #e74c3c;
            border-radius: 15px;
            padding: 30px;
            margin: 30px 0;
        }

        .tip-box {
            background: #e8f5e8;
            border: 3px solid #27ae60;
            border-radius: 15px;
            padding: 30px;
            margin: 30px 0;
        }

        .two-column {
            display: flex;
            gap: 50px;
            align-items: flex-start;
        }

        .column {
            flex: 1;
        }

        .center {
            text-align: center;
        }

        .large-text {
            font-size: 2em;
            font-weight: 500;
        }

        @media print {
            .slide {
                page-break-after: always;
                -webkit-print-color-adjust: exact;
                print-color-adjust: exact;
            }
        }
    </style>
</head>
<body>

<!-- Slide 1: Title -->
<div class="slide title-slide">
    <h1>Python Functions</h1>
    <div class="subtitle">构建模块化编程的基础</div>
    <div class="info">
        <p>计算机科学课程 - 45分钟</p>
        <p>大学级别</p>
    </div>
    <div class="slide-number">1/25</div>
</div>

<!-- Slide 2: Course Outline -->
<div class="slide content">
    <h2>课程大纲</h2>
    <ul>
        <li><span class="highlight">函数基础</span> - 什么是函数？</li>
        <li><span class="highlight">函数语法</span> - 基本结构</li>
        <li><span class="highlight">参数传递</span> - 数据输入</li>
        <li><span class="highlight">返回值</span> - 结果输出</li>
        <li><span class="highlight">作用域</span> - 变量范围</li>
        <li><span class="highlight">高级特性</span> - 默认参数、可变参数</li>
        <li><span class="highlight">最佳实践</span> - 编写优雅函数</li>
        <li><span class="highlight">实际应用</span> - 项目案例</li>
    </ul>
    <div class="slide-number">2/25</div>
</div>

<!-- Slide 3: What are Functions? -->
<div class="slide content">
    <h2>什么是函数？</h2>
    
    <div class="large-text center" style="margin: 50px 0;">
        函数是一个<span class="highlight">可重用的代码块</span>，<br>
        用于执行特定的任务
    </div>
    
    <div class="example-box center">
        <h3>🔧 就像现实中的工具</h3>
        <p>输入 → 处理 → 输出</p>
    </div>
    
    <div class="slide-number">3/25</div>
</div>

<!-- Slide 4: Why Use Functions? -->
<div class="slide content">
    <h2>为什么使用函数？</h2>
    
    <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 40px; margin-top: 50px;">
        <div class="tip-box">
            <h3>🔄 可重用性</h3>
            <p>写一次，用多次</p>
        </div>
        
        <div class="tip-box">
            <h3>📦 模块化</h3>
            <p>将复杂问题分解</p>
        </div>
        
        <div class="tip-box">
            <h3>🧹 代码整洁</h3>
            <p>保持代码有序</p>
        </div>
        
        <div class="tip-box">
            <h3>🐛 易于调试</h3>
            <p>隔离和修复问题</p>
        </div>
    </div>
    
    <div class="slide-number">4/25</div>
</div>

<!-- Slide 5: Basic Function Syntax -->
<div class="slide content">
    <h2>基本函数语法</h2>
    
    <pre><code class="language-python">def function_name(parameters):
    """
    函数文档字符串（可选）
    描述函数的功能
    """
    # 函数体 - 执行的代码
    
    return result  # 返回值（可选）</code></pre>
    
    <div class="slide-number">5/25</div>
</div>

<!-- Slide 6: Function Components -->
<div class="slide content">
    <h2>函数组成部分</h2>

    <div style="margin-top: 50px;">
        <div class="highlight" style="display: block; margin: 20px 0; font-size: 1.8em;">def</div>
        <p style="margin-left: 50px;">定义函数的关键字</p>

        <div class="highlight" style="display: block; margin: 20px 0; font-size: 1.8em;">function_name</div>
        <p style="margin-left: 50px;">函数名称（遵循命名规范）</p>

        <div class="highlight" style="display: block; margin: 20px 0; font-size: 1.8em;">parameters</div>
        <p style="margin-left: 50px;">参数列表（可选）</p>

        <div class="highlight" style="display: block; margin: 20px 0; font-size: 1.8em;">return</div>
        <p style="margin-left: 50px;">返回语句（可选）</p>
    </div>

    <div class="slide-number">6/25</div>
</div>

<!-- Slide 7: Your First Function -->
<div class="slide content">
    <h2>第一个函数</h2>

    <pre><code class="language-python">def say_hello():
    """
    打印问候语
    """
    print("Hello, Python!")
    print("欢迎学习函数！")</code></pre>

    <h3>调用函数：</h3>
    <pre><code class="language-python">say_hello()</code></pre>

    <div class="slide-number">7/25</div>
</div>

<!-- Slide 8: Function Output -->
<div class="slide content">
    <h2>函数输出</h2>

    <div class="example-box">
        <h3>运行结果：</h3>
        <pre style="background: #2d3748; color: #e2e8f0; padding: 20px; border-radius: 8px; font-size: 1.4em;">Hello, Python!
欢迎学习函数！</pre>
    </div>

    <div class="tip-box">
        <strong>💡 重要提示：</strong> 函数必须先定义，后调用！
    </div>

    <div class="slide-number">8/25</div>
</div>

<!-- Slide 9: Functions with Parameters -->
<div class="slide content">
    <h2>带参数的函数</h2>

    <pre><code class="language-python">def greet_person(name):
    """
    向特定的人问候
    """
    print(f"你好, {name}!")
    print("很高兴认识你！")</code></pre>

    <h3>函数调用：</h3>
    <pre><code class="language-python">greet_person("小明")
greet_person("小红")</code></pre>

    <div class="slide-number">9/25</div>
</div>

<!-- Slide 10: Multiple Parameters -->
<div class="slide content">
    <h2>多个参数</h2>

    <pre><code class="language-python">def introduce_person(name, age, city):
    """
    介绍一个人的详细信息
    """
    print(f"大家好，我是{name}")
    print(f"我今年{age}岁")
    print(f"我来自{city}")</code></pre>

    <h3>调用示例：</h3>
    <pre><code class="language-python">introduce_person("张三", 20, "北京")</code></pre>

    <div class="slide-number">10/25</div>
</div>

<!-- Slide 11: Parameters vs Arguments -->
<div class="slide content">
    <h2>参数 vs 实参</h2>

    <div class="two-column">
        <div class="column">
            <div class="example-box">
                <h3>参数 (Parameters)</h3>
                <p>函数定义时的变量</p>
                <pre><code class="language-python">def func(name, age):
    #    ↑     ↑
    # 这些是参数</code></pre>
            </div>
        </div>

        <div class="column">
            <div class="example-box">
                <h3>实参 (Arguments)</h3>
                <p>调用函数时传入的值</p>
                <pre><code class="language-python">func("小明", 18)
#     ↑     ↑
# 这些是实参</code></pre>
            </div>
        </div>
    </div>

    <div class="slide-number">11/25</div>
</div>

<!-- Slide 12: Return Values -->
<div class="slide content">
    <h2>返回值</h2>

    <pre><code class="language-python">def add_numbers(a, b):
    """
    计算两个数的和
    """
    result = a + b
    return result</code></pre>

    <h3>使用返回值：</h3>
    <pre><code class="language-python">sum_result = add_numbers(5, 3)
print(f"结果是: {sum_result}")  # 输出: 结果是: 8</code></pre>

    <div class="slide-number">12/25</div>
</div>

<!-- Slide 13: Multiple Return Values -->
<div class="slide content">
    <h2>多个返回值</h2>

    <pre><code class="language-python">def get_name_parts(full_name):
    """
    分割姓名
    """
    parts = full_name.split()
    first_name = parts[0]
    last_name = parts[-1]
    return first_name, last_name</code></pre>

    <h3>接收多个返回值：</h3>
    <pre><code class="language-python">first, last = get_name_parts("张 三")
print(f"姓: {first}, 名: {last}")</code></pre>

    <div class="slide-number">13/25</div>
</div>

<!-- Slide 14: Variable Scope - Global -->
<div class="slide content">
    <h2>变量作用域 - 全局变量</h2>

    <pre><code class="language-python"># 全局变量
global_var = "我是全局变量"

def my_function():
    print(f"函数内部: {global_var}")

my_function()  # 可以访问全局变量
print(f"函数外部: {global_var}")</code></pre>

    <div class="example-box">
        <strong>输出：</strong><br>
        函数内部: 我是全局变量<br>
        函数外部: 我是全局变量
    </div>

    <div class="slide-number">14/25</div>
</div>

<!-- Slide 15: Variable Scope - Local -->
<div class="slide content">
    <h2>变量作用域 - 局部变量</h2>

    <pre><code class="language-python">def my_function():
    local_var = "我是局部变量"  # 局部变量
    print(f"函数内部: {local_var}")

my_function()
# print(local_var)  # 错误！无法访问局部变量</code></pre>

    <div class="warning-box">
        <strong>⚠️ 注意：</strong> 局部变量只能在函数内部使用！
    </div>

    <div class="slide-number">15/25</div>
</div>

<!-- Slide 16: Modifying Global Variables -->
<div class="slide content">
    <h2>修改全局变量</h2>

    <pre><code class="language-python">counter = 0  # 全局变量

def increment():
    global counter  # 声明使用全局变量
    counter += 1
    print(f"计数器: {counter}")

increment()  # 计数器: 1
increment()  # 计数器: 2</code></pre>

    <div class="tip-box">
        <strong>💡 最佳实践：</strong> 尽量避免使用全局变量，通过参数传递数据
    </div>

    <div class="slide-number">16/25</div>
</div>

<!-- Slide 17: Default Parameters -->
<div class="slide content">
    <h2>默认参数</h2>

    <pre><code class="language-python">def greet_with_title(name, title="同学"):
    """
    带称谓的问候
    """
    print(f"你好, {title} {name}!")

# 不同的调用方式
greet_with_title("小明")           # 使用默认值
greet_with_title("小红", "老师")    # 自定义称谓</code></pre>

    <div class="example-box">
        <strong>输出：</strong><br>
        你好, 同学 小明!<br>
        你好, 老师 小红!
    </div>

    <div class="slide-number">17/25</div>
</div>

<!-- Slide 18: Multiple Default Parameters -->
<div class="slide content">
    <h2>多个默认参数</h2>

    <pre><code class="language-python">def create_profile(name, age=18, city="未知"):
    """
    创建用户档案
    """
    return {
        'name': name,
        'age': age,
        'city': city
    }</code></pre>

    <h3>灵活调用：</h3>
    <pre><code class="language-python">profile1 = create_profile("小明")
profile2 = create_profile("小红", 20)
profile3 = create_profile("小李", city="上海")</code></pre>

    <div class="slide-number">18/25</div>
</div>

<!-- Slide 19: *args - Variable Arguments -->
<div class="slide content">
    <h2>*args - 可变参数</h2>

    <pre><code class="language-python">def sum_all(*args):
    """
    计算任意数量参数的和
    """
    total = 0
    for num in args:
        total += num
    return total</code></pre>

    <h3>灵活调用：</h3>
    <pre><code class="language-python">print(sum_all(1, 2, 3))        # 6
print(sum_all(1, 2, 3, 4, 5))  # 15
print(sum_all(10))             # 10</code></pre>

    <div class="slide-number">19/25</div>
</div>

<!-- Slide 20: **kwargs - Keyword Arguments -->
<div class="slide content">
    <h2>**kwargs - 关键字参数</h2>

    <pre><code class="language-python">def create_student(**kwargs):
    """
    创建学生信息
    """
    student = {}
    for key, value in kwargs.items():
        student[key] = value
    return student</code></pre>

    <h3>使用示例：</h3>
    <pre><code class="language-python">student = create_student(
    name="小明",
    age=20,
    major="计算机科学",
    gpa=3.8
)</code></pre>

    <div class="slide-number">20/25</div>
</div>

<!-- Slide 21: Lambda Functions -->
<div class="slide content">
    <h2>Lambda 函数</h2>

    <div class="center large-text" style="margin: 50px 0;">
        Lambda = <span class="highlight">匿名函数</span>
    </div>

    <pre><code class="language-python"># 普通函数
def square(x):
    return x ** 2

# Lambda 等价写法
square_lambda = lambda x: x ** 2

print(square(5))        # 25
print(square_lambda(5)) # 25</code></pre>

    <div class="slide-number">21/25</div>
</div>

<!-- Slide 22: Lambda with Built-in Functions -->
<div class="slide content">
    <h2>Lambda 与内置函数</h2>

    <pre><code class="language-python">numbers = [1, 2, 3, 4, 5]

# 使用 map()
squared = list(map(lambda x: x**2, numbers))
print(squared)  # [1, 4, 9, 16, 25]

# 使用 filter()
even = list(filter(lambda x: x % 2 == 0, numbers))
print(even)     # [2, 4]</code></pre>

    <div class="tip-box">
        <strong>💡 适用场景：</strong> 简单的一行表达式
    </div>

    <div class="slide-number">22/25</div>
</div>

<!-- Slide 23: Best Practices -->
<div class="slide content">
    <h2>最佳实践</h2>

    <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 30px; margin-top: 40px;">
        <div class="tip-box">
            <h3>✅ 好的函数名</h3>
            <pre><code class="language-python">def calculate_total_price():
    pass

def is_valid_email():
    pass</code></pre>
        </div>

        <div class="warning-box">
            <h3>❌ 不好的函数名</h3>
            <pre><code class="language-python">def calc():  # 太短
    pass

def doStuff():  # 不清楚
    pass</code></pre>
        </div>
    </div>

    <div class="slide-number">23/25</div>
</div>

<!-- Slide 24: Function Documentation -->
<div class="slide content">
    <h2>函数文档</h2>

    <pre><code class="language-python">def calculate_interest(principal, rate, time):
    """
    计算复利

    参数:
        principal (float): 本金
        rate (float): 年利率 (小数形式)
        time (int): 年数

    返回:
        float: 最终金额

    示例:
        >>> calculate_interest(1000, 0.05, 2)
        1102.5
    """
    amount = principal * (1 + rate) ** time
    return round(amount, 2)</code></pre>

    <div class="slide-number">24/25</div>
</div>

<!-- Slide 25: Summary and Practice -->
<div class="slide title-slide">
    <h1>总结与练习</h1>
    <div class="subtitle">让我们应用所学知识！</div>
    <div class="info">
        <p><strong>练习题：</strong></p>
        <ul style="text-align: left; max-width: 800px; margin: 0 auto; font-size: 1.4em;">
            <li>编写一个判断质数的函数</li>
            <li>创建一个字符串反转函数</li>
            <li>设计一个计算列表最大值的函数</li>
            <li>构建一个统计词频的函数</li>
        </ul>
    </div>
    <div class="slide-number">25/25</div>
</div>

</body>
</html>
