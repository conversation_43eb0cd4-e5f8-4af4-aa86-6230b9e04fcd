<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Python Functions - CS Course</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Arial', sans-serif;
            line-height: 1.6;
            color: #333;
        }

        .slide {
            width: 100vw;
            height: 100vh;
            padding: 60px;
            display: flex;
            flex-direction: column;
            justify-content: center;
            page-break-after: always;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }

        .slide.content {
            background: white;
            color: #333;
        }

        .slide h1 {
            font-size: 3.5em;
            margin-bottom: 30px;
            text-align: center;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }

        .slide h2 {
            font-size: 2.5em;
            margin-bottom: 25px;
            color: #2c3e50;
            border-bottom: 3px solid #3498db;
            padding-bottom: 10px;
        }

        .slide h3 {
            font-size: 1.8em;
            margin-bottom: 20px;
            color: #34495e;
        }

        .slide p, .slide li {
            font-size: 1.3em;
            margin-bottom: 15px;
            line-height: 1.8;
        }

        .slide ul {
            margin-left: 40px;
            margin-bottom: 20px;
        }

        .slide li {
            margin-bottom: 10px;
        }

        .code-block {
            background: #2c3e50;
            color: #ecf0f1;
            padding: 25px;
            border-radius: 10px;
            font-family: 'Courier New', monospace;
            font-size: 1.1em;
            margin: 20px 0;
            border-left: 5px solid #3498db;
            overflow-x: auto;
        }

        .highlight {
            background: #f39c12;
            color: white;
            padding: 5px 10px;
            border-radius: 5px;
            font-weight: bold;
        }

        .two-column {
            display: flex;
            gap: 40px;
        }

        .column {
            flex: 1;
        }

        .slide-number {
            position: absolute;
            bottom: 20px;
            right: 30px;
            font-size: 1.2em;
            opacity: 0.7;
        }

        .title-slide {
            text-align: center;
        }

        .title-slide h1 {
            font-size: 4em;
            margin-bottom: 20px;
        }

        .title-slide .subtitle {
            font-size: 1.8em;
            margin-bottom: 40px;
            opacity: 0.9;
        }

        .title-slide .info {
            font-size: 1.4em;
            opacity: 0.8;
        }

        .example-box {
            background: #ecf0f1;
            border: 2px solid #3498db;
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
        }

        .warning-box {
            background: #ffe6e6;
            border: 2px solid #e74c3c;
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
        }

        .tip-box {
            background: #e8f5e8;
            border: 2px solid #27ae60;
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
        }
    </style>
</head>
<body>

<!-- Slide 1: Title -->
<div class="slide title-slide">
    <h1>Python Functions</h1>
    <div class="subtitle">Building Blocks of Modular Programming</div>
    <div class="info">
        <p>CS Course - 45 Minutes</p>
        <p>University Level</p>
    </div>
    <div class="slide-number">1</div>
</div>

<!-- Slide 2: Course Outline -->
<div class="slide content">
    <h2>Course Outline</h2>
    <ul>
        <li><span class="highlight">What are Functions?</span> - Definition and Purpose</li>
        <li><span class="highlight">Function Syntax</span> - Basic Structure</li>
        <li><span class="highlight">Parameters & Arguments</span> - Passing Data</li>
        <li><span class="highlight">Return Values</span> - Getting Results Back</li>
        <li><span class="highlight">Scope & Variables</span> - Local vs Global</li>
        <li><span class="highlight">Advanced Topics</span> - Default Parameters, *args, **kwargs</li>
        <li><span class="highlight">Best Practices</span> - Writing Clean Functions</li>
        <li><span class="highlight">Practical Examples</span> - Real-world Applications</li>
    </ul>
    <div class="slide-number">2</div>
</div>

<!-- Slide 3: What are Functions? -->
<div class="slide content">
    <h2>What are Functions?</h2>
    <div class="two-column">
        <div class="column">
            <h3>Definition</h3>
            <p>A <span class="highlight">function</span> is a reusable block of code that performs a specific task.</p>
            
            <h3>Why Use Functions?</h3>
            <ul>
                <li><strong>Modularity:</strong> Break complex problems into smaller parts</li>
                <li><strong>Reusability:</strong> Write once, use many times</li>
                <li><strong>Organization:</strong> Keep code clean and structured</li>
                <li><strong>Testing:</strong> Easier to test individual components</li>
                <li><strong>Debugging:</strong> Isolate and fix issues</li>
            </ul>
        </div>
        <div class="column">
            <div class="example-box">
                <h3>Real-world Analogy</h3>
                <p>Think of a function like a <strong>recipe</strong>:</p>
                <ul>
                    <li>📝 <strong>Name:</strong> "Make Coffee"</li>
                    <li>🥄 <strong>Ingredients:</strong> Water, coffee beans</li>
                    <li>⚙️ <strong>Process:</strong> Grind, brew, serve</li>
                    <li>☕ <strong>Result:</strong> A cup of coffee</li>
                </ul>
            </div>
        </div>
    </div>
    <div class="slide-number">3</div>
</div>

<!-- Slide 4: Basic Function Syntax -->
<div class="slide content">
    <h2>Basic Function Syntax</h2>
    
    <div class="code-block">
def function_name(parameters):
    """
    Optional docstring - describes what the function does
    """
    # Function body - the code that runs
    # when the function is called
    
    return result  # Optional return statement
    </div>

    <h3>Key Components:</h3>
    <ul>
        <li><span class="highlight">def</span> - Keyword to define a function</li>
        <li><span class="highlight">function_name</span> - Descriptive name (follow naming conventions)</li>
        <li><span class="highlight">parameters</span> - Input values (optional)</li>
        <li><span class="highlight">docstring</span> - Documentation (optional but recommended)</li>
        <li><span class="highlight">return</span> - Output value (optional)</li>
    </ul>
    
    <div class="slide-number">4</div>
</div>

<!-- Slide 5: Simple Function Example -->
<div class="slide content">
    <h2>Your First Function</h2>
    
    <div class="two-column">
        <div class="column">
            <h3>Function Definition</h3>
            <div class="code-block">
def greet():
    """
    Prints a greeting message
    """
    print("Hello, World!")
    print("Welcome to Python!")

# Function call
greet()
            </div>
        </div>
        <div class="column">
            <h3>Output</h3>
            <div class="code-block">
Hello, World!
Welcome to Python!
            </div>
            
            <div class="tip-box">
                <strong>💡 Tip:</strong> Functions must be defined before they can be called!
            </div>
        </div>
    </div>
    
    <div class="slide-number">5</div>
</div>

<!-- Slide 6: Functions with Parameters -->
<div class="slide content">
    <h2>Functions with Parameters</h2>

    <div class="two-column">
        <div class="column">
            <h3>Single Parameter</h3>
            <div class="code-block">
def greet_person(name):
    """
    Greets a specific person
    """
    print(f"Hello, {name}!")
    print("Nice to meet you!")

# Function calls
greet_person("Alice")
greet_person("Bob")
            </div>
        </div>
        <div class="column">
            <h3>Multiple Parameters</h3>
            <div class="code-block">
def introduce(name, age, city):
    """
    Introduces a person with details
    """
    print(f"Hi, I'm {name}")
    print(f"I'm {age} years old")
    print(f"I live in {city}")

# Function call
introduce("Charlie", 25, "New York")
            </div>
        </div>
    </div>

    <div class="example-box">
        <strong>Parameters vs Arguments:</strong>
        <ul>
            <li><strong>Parameters:</strong> Variables in function definition (name, age, city)</li>
            <li><strong>Arguments:</strong> Actual values passed when calling ("Charlie", 25, "New York")</li>
        </ul>
    </div>

    <div class="slide-number">6</div>
</div>

<!-- Slide 7: Return Values -->
<div class="slide content">
    <h2>Return Values</h2>

    <div class="two-column">
        <div class="column">
            <h3>Functions that Return Values</h3>
            <div class="code-block">
def add_numbers(a, b):
    """
    Adds two numbers and returns result
    """
    result = a + b
    return result

def calculate_area(length, width):
    """
    Calculates rectangle area
    """
    return length * width

# Using returned values
sum_result = add_numbers(5, 3)
print(f"Sum: {sum_result}")

area = calculate_area(10, 5)
print(f"Area: {area}")
            </div>
        </div>
        <div class="column">
            <h3>Multiple Return Values</h3>
            <div class="code-block">
def get_name_parts(full_name):
    """
    Splits full name into parts
    """
    parts = full_name.split()
    first_name = parts[0]
    last_name = parts[-1]
    return first_name, last_name

# Unpacking returned values
first, last = get_name_parts("John Doe")
print(f"First: {first}")
print(f"Last: {last}")
            </div>

            <div class="warning-box">
                <strong>⚠️ Important:</strong> Functions without explicit return statement return <code>None</code>
            </div>
        </div>
    </div>

    <div class="slide-number">7</div>
</div>

<!-- Slide 8: Variable Scope -->
<div class="slide content">
    <h2>Variable Scope</h2>

    <div class="two-column">
        <div class="column">
            <h3>Local vs Global Variables</h3>
            <div class="code-block">
# Global variable
global_var = "I'm global"

def my_function():
    # Local variable
    local_var = "I'm local"
    print(f"Inside function: {global_var}")
    print(f"Inside function: {local_var}")

my_function()
print(f"Outside function: {global_var}")
# print(local_var)  # This would cause an error!
            </div>
        </div>
        <div class="column">
            <h3>Modifying Global Variables</h3>
            <div class="code-block">
counter = 0  # Global variable

def increment_counter():
    global counter
    counter += 1
    print(f"Counter: {counter}")

def reset_counter():
    global counter
    counter = 0
    print("Counter reset")

increment_counter()  # Counter: 1
increment_counter()  # Counter: 2
reset_counter()      # Counter reset
            </div>

            <div class="tip-box">
                <strong>💡 Best Practice:</strong> Avoid global variables when possible. Pass values as parameters instead.
            </div>
        </div>
    </div>

    <div class="slide-number">8</div>
</div>

<!-- Slide 9: Default Parameters -->
<div class="slide content">
    <h2>Default Parameters</h2>

    <div class="two-column">
        <div class="column">
            <h3>Basic Default Parameters</h3>
            <div class="code-block">
def greet_with_title(name, title="Mr./Ms."):
    """
    Greets person with optional title
    """
    print(f"Hello, {title} {name}!")

# Different ways to call
greet_with_title("Smith")           # Uses default
greet_with_title("Johnson", "Dr.")  # Custom title
greet_with_title("Brown", title="Prof.")  # Named argument
            </div>

            <h3>Multiple Defaults</h3>
            <div class="code-block">
def create_profile(name, age=18, city="Unknown"):
    """
    Creates user profile with defaults
    """
    return {
        'name': name,
        'age': age,
        'city': city
    }

profile1 = create_profile("Alice")
profile2 = create_profile("Bob", 25)
profile3 = create_profile("Carol", city="Boston")
            </div>
        </div>
        <div class="column">
            <h3>Important Rules</h3>
            <ul>
                <li>Default parameters must come <strong>after</strong> non-default parameters</li>
                <li>Default values are evaluated <strong>once</strong> when function is defined</li>
                <li>Be careful with mutable defaults (lists, dictionaries)</li>
            </ul>

            <div class="warning-box">
                <strong>⚠️ Mutable Default Trap:</strong>
                <div class="code-block">
# DON'T do this:
def add_item(item, my_list=[]):
    my_list.append(item)
    return my_list

# DO this instead:
def add_item(item, my_list=None):
    if my_list is None:
        my_list = []
    my_list.append(item)
    return my_list
                </div>
            </div>
        </div>
    </div>

    <div class="slide-number">9</div>
</div>

<!-- Slide 10: *args and **kwargs -->
<div class="slide content">
    <h2>Variable Arguments: *args and **kwargs</h2>

    <div class="two-column">
        <div class="column">
            <h3>*args - Variable Positional Arguments</h3>
            <div class="code-block">
def sum_all(*args):
    """
    Sums any number of arguments
    """
    total = 0
    for num in args:
        total += num
    return total

# Different number of arguments
print(sum_all(1, 2, 3))        # 6
print(sum_all(1, 2, 3, 4, 5))  # 15
print(sum_all(10))             # 10

def print_info(name, *hobbies):
    print(f"Name: {name}")
    print("Hobbies:", ", ".join(hobbies))

print_info("Alice", "reading", "swimming", "coding")
            </div>
        </div>
        <div class="column">
            <h3>**kwargs - Variable Keyword Arguments</h3>
            <div class="code-block">
def create_student(**kwargs):
    """
    Creates student with any attributes
    """
    student = {}
    for key, value in kwargs.items():
        student[key] = value
    return student

# Flexible function calls
student1 = create_student(
    name="Bob",
    age=20,
    major="CS"
)

student2 = create_student(
    name="Carol",
    age=19,
    major="Math",
    gpa=3.8,
    year="Sophomore"
)
            </div>

            <h3>Combining All Types</h3>
            <div class="code-block">
def flexible_func(required, default="default", *args, **kwargs):
    print(f"Required: {required}")
    print(f"Default: {default}")
    print(f"Args: {args}")
    print(f"Kwargs: {kwargs}")
            </div>
        </div>
    </div>

    <div class="slide-number">10</div>
</div>

<!-- Slide 11: Lambda Functions -->
<div class="slide content">
    <h2>Lambda Functions (Anonymous Functions)</h2>

    <div class="two-column">
        <div class="column">
            <h3>Basic Lambda Syntax</h3>
            <div class="code-block">
# Regular function
def square(x):
    return x ** 2

# Lambda equivalent
square_lambda = lambda x: x ** 2

print(square(5))        # 25
print(square_lambda(5)) # 25

# Multiple parameters
add = lambda x, y: x + y
print(add(3, 4))        # 7
            </div>

            <h3>Common Use Cases</h3>
            <div class="code-block">
# With map()
numbers = [1, 2, 3, 4, 5]
squared = list(map(lambda x: x**2, numbers))
print(squared)  # [1, 4, 9, 16, 25]

# With filter()
even_numbers = list(filter(lambda x: x % 2 == 0, numbers))
print(even_numbers)  # [2, 4]

# With sorted()
students = [('Alice', 85), ('Bob', 90), ('Carol', 78)]
sorted_by_grade = sorted(students, key=lambda x: x[1])
print(sorted_by_grade)
            </div>
        </div>
        <div class="column">
            <h3>When to Use Lambda</h3>
            <ul>
                <li><strong>Short, simple functions</strong> (one expression)</li>
                <li><strong>Temporary functions</strong> for map, filter, sort</li>
                <li><strong>Functional programming</strong> patterns</li>
            </ul>

            <div class="warning-box">
                <strong>⚠️ Lambda Limitations:</strong>
                <ul>
                    <li>Only single expressions (no statements)</li>
                    <li>No docstrings</li>
                    <li>Can be less readable for complex logic</li>
                </ul>
            </div>

            <div class="tip-box">
                <strong>💡 Best Practice:</strong> Use lambda for simple operations. For complex logic, use regular functions with descriptive names.
            </div>
        </div>
    </div>

    <div class="slide-number">11</div>
</div>

<!-- Slide 12: Function Best Practices -->
<div class="slide content">
    <h2>Function Best Practices</h2>

    <div class="two-column">
        <div class="column">
            <h3>1. Naming Conventions</h3>
            <div class="code-block">
# Good function names
def calculate_total_price():
    pass

def get_user_input():
    pass

def is_valid_email():
    pass

# Bad function names
def calc():  # Too short
    pass

def doStuff():  # Not descriptive
    pass

def GetUserInput():  # Wrong case
    pass
            </div>

            <h3>2. Function Size</h3>
            <ul>
                <li>Keep functions <strong>small and focused</strong></li>
                <li>One function = <strong>one responsibility</strong></li>
                <li>Aim for <strong>20-30 lines</strong> maximum</li>
                <li>If longer, consider breaking into smaller functions</li>
            </ul>
        </div>
        <div class="column">
            <h3>3. Documentation</h3>
            <div class="code-block">
def calculate_compound_interest(principal, rate, time, compounds_per_year=1):
    """
    Calculate compound interest.

    Args:
        principal (float): Initial amount of money
        rate (float): Annual interest rate (as decimal)
        time (int): Number of years
        compounds_per_year (int): Compounding frequency

    Returns:
        float: Final amount after compound interest

    Example:
        >>> calculate_compound_interest(1000, 0.05, 2)
        1102.5
    """
    amount = principal * (1 + rate/compounds_per_year) ** (compounds_per_year * time)
    return round(amount, 2)
            </div>

            <h3>4. Error Handling</h3>
            <div class="code-block">
def divide_numbers(a, b):
    """
    Safely divide two numbers
    """
    if b == 0:
        raise ValueError("Cannot divide by zero")

    if not isinstance(a, (int, float)) or not isinstance(b, (int, float)):
        raise TypeError("Arguments must be numbers")

    return a / b
            </div>
        </div>
    </div>

    <div class="slide-number">12</div>
</div>

<!-- Slide 13: Practical Example - Calculator -->
<div class="slide content">
    <h2>Practical Example: Simple Calculator</h2>

    <div class="code-block">
def add(x, y):
    """Add two numbers"""
    return x + y

def subtract(x, y):
    """Subtract two numbers"""
    return x - y

def multiply(x, y):
    """Multiply two numbers"""
    return x * y

def divide(x, y):
    """Divide two numbers"""
    if y == 0:
        return "Error: Division by zero!"
    return x / y

def calculator():
    """Simple calculator with menu"""
    operations = {
        '1': ('Addition', add),
        '2': ('Subtraction', subtract),
        '3': ('Multiplication', multiply),
        '4': ('Division', divide)
    }

    print("Simple Calculator")
    print("1. Addition")
    print("2. Subtraction")
    print("3. Multiplication")
    print("4. Division")

    choice = input("Enter choice (1-4): ")

    if choice in operations:
        num1 = float(input("Enter first number: "))
        num2 = float(input("Enter second number: "))

        operation_name, operation_func = operations[choice]
        result = operation_func(num1, num2)

        print(f"{operation_name}: {num1} and {num2} = {result}")
    else:
        print("Invalid choice!")

# Run the calculator
calculator()
    </div>

    <div class="slide-number">13</div>
</div>

<!-- Slide 14: Practical Example - Data Processing -->
<div class="slide content">
    <h2>Practical Example: Student Grade Processing</h2>

    <div class="code-block">
def calculate_average(grades):
    """Calculate average of grades"""
    if not grades:
        return 0
    return sum(grades) / len(grades)

def get_letter_grade(average):
    """Convert numeric average to letter grade"""
    if average >= 90:
        return 'A'
    elif average >= 80:
        return 'B'
    elif average >= 70:
        return 'C'
    elif average >= 60:
        return 'D'
    else:
        return 'F'

def process_student_data(students_data):
    """Process multiple students' grade data"""
    results = []

    for student in students_data:
        name = student['name']
        grades = student['grades']

        average = calculate_average(grades)
        letter_grade = get_letter_grade(average)

        results.append({
            'name': name,
            'average': round(average, 2),
            'letter_grade': letter_grade,
            'status': 'Pass' if average >= 60 else 'Fail'
        })

    return results

# Example usage
students = [
    {'name': 'Alice', 'grades': [85, 92, 78, 96]},
    {'name': 'Bob', 'grades': [72, 68, 75, 70]},
    {'name': 'Carol', 'grades': [95, 98, 92, 94]}
]

results = process_student_data(students)
for result in results:
    print(f"{result['name']}: {result['average']} ({result['letter_grade']}) - {result['status']}")
    </div>

    <div class="slide-number">14</div>
</div>

<!-- Slide 15: Common Mistakes and Debugging -->
<div class="slide content">
    <h2>Common Mistakes and Debugging</h2>

    <div class="two-column">
        <div class="column">
            <h3>1. Forgetting to Return</h3>
            <div class="code-block">
# Wrong - prints but doesn't return
def add_wrong(a, b):
    print(a + b)

# Correct - returns the value
def add_correct(a, b):
    return a + b

result = add_wrong(3, 4)    # result is None
result = add_correct(3, 4)  # result is 7
            </div>

            <h3>2. Modifying Mutable Parameters</h3>
            <div class="code-block">
# Dangerous - modifies original list
def add_item_wrong(my_list, item):
    my_list.append(item)
    return my_list

# Safe - creates new list
def add_item_safe(my_list, item):
    new_list = my_list.copy()
    new_list.append(item)
    return new_list
            </div>
        </div>
        <div class="column">
            <h3>3. Variable Scope Issues</h3>
            <div class="code-block">
# Wrong - trying to modify global without declaration
count = 0
def increment_wrong():
    count += 1  # UnboundLocalError

# Correct - using global keyword
def increment_correct():
    global count
    count += 1

# Better - return new value
def increment_better(current_count):
    return current_count + 1
            </div>

            <h3>Debugging Tips</h3>
            <ul>
                <li>Use <span class="highlight">print()</span> statements to trace execution</li>
                <li>Check function <span class="highlight">inputs and outputs</span></li>
                <li>Test functions with <span class="highlight">simple examples</span> first</li>
                <li>Use Python debugger (<span class="highlight">pdb</span>) for complex issues</li>
                <li>Write <span class="highlight">unit tests</span> for your functions</li>
            </ul>
        </div>
    </div>

    <div class="slide-number">15</div>
</div>

<!-- Slide 16: Summary and Next Steps -->
<div class="slide content">
    <h2>Summary</h2>

    <div class="two-column">
        <div class="column">
            <h3>What We Covered</h3>
            <ul>
                <li>✅ Function definition and syntax</li>
                <li>✅ Parameters and arguments</li>
                <li>✅ Return values</li>
                <li>✅ Variable scope (local vs global)</li>
                <li>✅ Default parameters</li>
                <li>✅ *args and **kwargs</li>
                <li>✅ Lambda functions</li>
                <li>✅ Best practices</li>
                <li>✅ Practical examples</li>
                <li>✅ Common mistakes</li>
            </ul>
        </div>
        <div class="column">
            <h3>Key Takeaways</h3>
            <div class="tip-box">
                <ul>
                    <li><strong>Functions make code reusable and organized</strong></li>
                    <li><strong>Use descriptive names and documentation</strong></li>
                    <li><strong>Keep functions small and focused</strong></li>
                    <li><strong>Understand scope and parameter passing</strong></li>
                    <li><strong>Practice with real examples</strong></li>
                </ul>
            </div>

            <h3>Next Steps</h3>
            <ul>
                <li>🔍 <strong>Practice:</strong> Write functions for your projects</li>
                <li>📚 <strong>Learn:</strong> Decorators and advanced topics</li>
                <li>🧪 <strong>Explore:</strong> Functional programming concepts</li>
                <li>🏗️ <strong>Build:</strong> Larger programs using functions</li>
            </ul>
        </div>
    </div>

    <div class="slide-number">16</div>
</div>

<!-- Slide 17: Questions and Practice -->
<div class="slide title-slide">
    <h1>Questions & Practice</h1>
    <div class="subtitle">Let's Apply What We've Learned!</div>
    <div class="info">
        <p>Practice Exercises:</p>
        <ul style="text-align: left; max-width: 600px; margin: 0 auto;">
            <li>Write a function to check if a number is prime</li>
            <li>Create a function that reverses a string</li>
            <li>Build a function to find the maximum value in a list</li>
            <li>Design a function that counts word frequency in text</li>
        </ul>
    </div>
    <div class="slide-number">17</div>
</div>

</body>
</html>
